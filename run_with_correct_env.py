import os
import sys
import subprocess

def main():
    """Run the main pipeline with correct environment variables from .env file"""
    
    # Load .env file manually and override system environment
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
        
        print("✓ Loaded environment variables from .env file:")
        for key in env_vars:
            if 'KEY' in key:
                print(f"  {key}: {env_vars[key][:50]}...")
            else:
                print(f"  {key}: {env_vars[key]}")
    
    except FileNotFoundError:
        print("❌ .env file not found")
        return 1
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return 1
    
    # Create new environment with correct variables
    new_env = os.environ.copy()
    new_env.update(env_vars)
    
    # Determine which script to run
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            script = 'test_database.py'
        elif sys.argv[1] == 'network':
            script = 'test_network.py'
        elif sys.argv[1] == 'main':
            script = 'main.py'
            if len(sys.argv) > 2 and sys.argv[2] == '-dev':
                script += ' -dev'
        else:
            print("Usage: python run_with_correct_env.py [test|network|main] [-dev]")
            return 1
    else:
        script = 'main.py -dev'  # Default to development mode
    
    print(f"\n🚀 Running: python {script}")
    print("=" * 50)
    
    try:
        # Run the script with correct environment
        result = subprocess.run(
            f"python {script}",
            shell=True,
            env=new_env,
            cwd=os.getcwd()
        )
        return result.returncode
    except Exception as e:
        print(f"❌ Error running script: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
