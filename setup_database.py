import os
import sys
from logging import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, INFO

def setup_environment():
    """Load environment variables from .env file"""
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
        
        # Override system environment with .env values
        os.environ.update(env_vars)
        print("✓ Environment variables loaded from .env file")
        return True
    except FileNotFoundError:
        print("❌ .env file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def setup_logger():
    """Setup basic logger"""
    logger = Logger("database_setup")
    logger.setLevel(INFO)
    
    console_handler = StreamHandler()
    console_handler.setLevel(INFO)
    console_formatter = Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    return logger

def create_music_archive_table():
    """Create the music_archive table in Supabase"""
    logger = setup_logger()
    
    # Check environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        logger.error("❌ SUPABASE_URL and SUPABASE_KEY environment variables must be set")
        return False
    
    try:
        from supabase import create_client
        
        # Initialize Supabase client
        supabase = create_client(supabase_url, supabase_key)
        logger.info("✓ Supabase client created successfully")
        
        # Create the table using SQL
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS music_archive (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            chart_date DATE NOT NULL,
            chart_type TEXT NOT NULL,
            chart_data JSONB NOT NULL,
            created_at TIMESTAMPTZ DEFAULT (now() AT TIME ZONE 'utc'::text)
        );
        
        CREATE UNIQUE INDEX IF NOT EXISTS idx_music_archive_unique
        ON music_archive(chart_date, chart_type);
        """
        
        logger.info("Creating music_archive table...")
        
        # Execute the SQL using Supabase RPC or direct SQL execution
        # Note: Supabase Python client doesn't directly support DDL, so we'll use the REST API
        import requests
        
        headers = {
            "apikey": supabase_key,
            "Authorization": f"Bearer {supabase_key}",
            "Content-Type": "application/json"
        }
        
        # Use Supabase's SQL function endpoint
        sql_payload = {
            "query": create_table_sql
        }
        
        response = requests.post(
            f"{supabase_url}/rest/v1/rpc/exec_sql",
            headers=headers,
            json=sql_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            logger.info("✓ Table creation SQL executed successfully")
        else:
            # Try alternative approach - create table through direct SQL execution
            logger.info("Trying alternative table creation method...")
            
            # Create a simple test record to trigger table creation
            test_record = {
                "chart_date": "2024-01-01",
                "chart_type": "setup_test",
                "chart_data": [{"test": "data"}]
            }
            
            try:
                result = supabase.table("music_archive").insert(test_record).execute()
                logger.info("✓ Table created successfully via insert operation")
                
                # Clean up test record
                supabase.table("music_archive").delete().eq("chart_type", "setup_test").execute()
                logger.info("✓ Test record cleaned up")
                
            except Exception as e:
                if "does not exist" in str(e):
                    logger.error("❌ Table does not exist and cannot be created automatically")
                    logger.info("Please create the table manually in your Supabase dashboard:")
                    logger.info("SQL to run in Supabase SQL Editor:")
                    logger.info(create_table_sql)
                    return False
                else:
                    raise e
        
        # Verify table exists by querying it
        logger.info("Verifying table exists...")
        response = supabase.table("music_archive").select("*").limit(1).execute()
        logger.info("✓ Table verification successful")
        
        # Show table info
        logger.info("📊 Table 'music_archive' is ready!")
        logger.info("Columns: id (uuid), chart_date (date), chart_type (text), chart_data (jsonb), created_at (timestamptz)")
        
        return True
        
    except ImportError:
        logger.error("❌ supabase-py library not installed")
        logger.info("Install it with: pip install supabase")
        return False
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        logger.info("\nManual setup instructions:")
        logger.info("1. Go to your Supabase dashboard")
        logger.info("2. Open the SQL Editor")
        logger.info("3. Run this SQL:")
        logger.info(create_table_sql)
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("🛠️  SUPABASE DATABASE SETUP")
    print("=" * 60)
    
    # Setup environment
    if not setup_environment():
        return 1
    
    # Create table
    success = create_music_archive_table()
    
    print("=" * 60)
    if success:
        print("✅ DATABASE SETUP COMPLETED!")
        print("You can now run: python run_with_correct_env.py test")
        print("Or run the main pipeline: python run_with_correct_env.py main -dev")
    else:
        print("❌ DATABASE SETUP FAILED!")
        print("Please follow the manual setup instructions above.")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
