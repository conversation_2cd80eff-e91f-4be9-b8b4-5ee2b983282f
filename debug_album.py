import requests
from bs4 import BeautifulSoup

url = 'https://musicchartsarchive.com/album-chart/2024-01-06'
print(f"Fetching: {url}")

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}

try:
    response = requests.get(url, headers=headers, timeout=10)
    print(f"Status code: {response.status_code}")

    soup = BeautifulSoup(response.text, 'html.parser')

    # Find all links
    all_links = soup.find_all('a', href=True)
    print(f"Total links found: {len(all_links)}")

    # Find album links
    album_links = [link for link in all_links if '/albums/' in link.get('href', '')]
    print(f"Album links found: {len(album_links)}")

    for i, link in enumerate(album_links[:5]):
        href = link.get('href', '')
        text = link.get_text(strip=True)
        print(f'{i+1}. Text: "{text}"')
        print(f'   Href: {href}')

        # Extract artist from URL
        if href:
            url_parts = href.strip('/').split('/')
            print(f'   URL parts: {url_parts}')
            if len(url_parts) >= 3:
                artist_slug = url_parts[1]
                artist = artist_slug.replace('-', ' ').title()
                print(f'   Artist from URL: {artist}')
        print()

except Exception as e:
    print(f"Error: {e}")
