postgrest-0.17.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-0.17.2.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
postgrest-0.17.2.dist-info/METADATA,sha256=NQ6csoWtlpDLNJuRHZ8vG10R-z-mNwfno_jIqNbBczQ,3408
postgrest-0.17.2.dist-info/RECORD,,
postgrest-0.17.2.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
postgrest/__init__.py,sha256=30MCuUqc_tEQZxJFN01F9DXucq1iMMKDeMoW43TYSwk,950
postgrest/__pycache__/__init__.cpython-311.pyc,,
postgrest/__pycache__/base_client.cpython-311.pyc,,
postgrest/__pycache__/base_request_builder.cpython-311.pyc,,
postgrest/__pycache__/constants.cpython-311.pyc,,
postgrest/__pycache__/deprecated_client.cpython-311.pyc,,
postgrest/__pycache__/deprecated_get_request_builder.cpython-311.pyc,,
postgrest/__pycache__/exceptions.cpython-311.pyc,,
postgrest/__pycache__/types.cpython-311.pyc,,
postgrest/__pycache__/utils.cpython-311.pyc,,
postgrest/__pycache__/version.cpython-311.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-311.pyc,,
postgrest/_async/__pycache__/client.cpython-311.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-311.pyc,,
postgrest/_async/client.py,sha256=sKeT37vnWW2X1p31tGG_HT3f60BoDlHFK-Vz1F_WdqE,4147
postgrest/_async/request_builder.py,sha256=P_BvbR530t2i8BdNxGyj_vnPG_KaHFSJC0dO4WirE8I,14195
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-311.pyc,,
postgrest/_sync/__pycache__/client.cpython-311.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-311.pyc,,
postgrest/_sync/client.py,sha256=gZn_TsLad0zGLkhSgl4lThv3hiEkQC7Hk0j7Ub2yE9I,4101
postgrest/_sync/request_builder.py,sha256=C47QIR91laQ-rbkJ9SbxPiJArUggPBuqRLy-0TrErII,14127
postgrest/base_client.py,sha256=9m2vbmcXp--DEhvw3OKnVN7mtqjKL3LQmusrdcOo5Jc,2065
postgrest/base_request_builder.py,sha256=h5-Gux3D8K7G9Qf7PApDovmKmm5oTFDOXutrq72R8pk,23557
postgrest/constants.py,sha256=VZrlQtgGV-qwcjwqhlJOZBhPHzbSICjDJbabcAlPMUY,153
postgrest/deprecated_client.py,sha256=6sC3m36fiUrwORHYOSyXjUXC21f4BfdTCEMgIedN8qE,416
postgrest/deprecated_get_request_builder.py,sha256=ycFiTJSfO4sWlQGSQMPVWj3oXLQAv1FVIi0vgT8o26A,429
postgrest/exceptions.py,sha256=T4ORME29C_CyIJUCWlDXoS3qSyv7LxMov1xLyMGvfho,1510
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=o1CCqNb7TAfdaCoYBF137kDio37dWszGNRaQJsDhHqY,986
postgrest/utils.py,sha256=_9qLBJvOImBRkln1dJk6k236CcmcYOajhJMS-b5czmw,1152
postgrest/version.py,sha256=YtZXQKWt-Fnr-a6W9x-kMdaqznzjt5A2rLjUmORs88Q,53
