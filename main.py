import os
import sys
from logging import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>atter, INFO, ERROR

from config.config_loader import load_config
from libs.clients.music_charts_client import MusicChartsClient
from libs.logging.slack_log_handler import <PERSON>lackLogHandler
from libs.logging.slack import Slack
from raw.musiccharts_etl import raw_musiccharts_job


def setup_environment():
    is_dev = len(sys.argv) > 1 and sys.argv[1] == '-dev'

    if is_dev:
        try:
            from dotenv import load_dotenv
            load_dotenv()
            print("✓ Environment variables loaded from .env file")
        except ImportError:
            print("⚠ python-dotenv not installed. Using system environment variables.")
        except Exception as e:
            print(f"⚠ Error loading .env file: {e}")

    # Always try to load .env file manually if dotenv fails or isn't available
    try:
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()

        # Override system environment with .env values
        os.environ.update(env_vars)
        if not is_dev:  # Only print if not already printed above
            print("✓ Environment variables loaded from .env file")
    except FileNotFoundError:
        print("⚠ .env file not found. Using system environment variables.")
    except Exception as e:
        print(f"⚠ Error reading .env file manually: {e}")

    return is_dev


def load_environment_variables():
    return {
        'supabase_url': os.getenv("SUPABASE_URL", ""),
        'supabase_key': os.getenv("SUPABASE_KEY", ""),
        'slack_webhook_url': os.getenv("SLACK_WEBHOOK_URL", "")
    }


def load_application_config(is_dev):
    try:
        environment = "dev" if is_dev else "prod"
        config = load_config(environment)

        if not config:
            print(f"Failed to load {environment} configuration")
            return None

        return config
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return None


def extract_config_values(config):
    try:
        return {
            'tables': config["database_tables"],
            'read_batch_size': config["constants"]["read_batch_size"],
            'write_batch_size': config["constants"]["write_batch_size"],
            'raw_music_charts_config': config["jobs"]["raw_music_charts"]
        }
    except KeyError as e:
        print(f"Missing required configuration key: {e}")
        return None

def setup_logging(is_dev, slack_webhook_url):
    logger = Logger("music_charts_pipeline")
    logger.setLevel(INFO)

    console_handler = StreamHandler()
    console_handler.setLevel(INFO)
    console_formatter = Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    if not is_dev and slack_webhook_url:
        try:
            slack = Slack(slack_webhook_url)
            slack_handler = SlackLogHandler(slack)
            slack_handler.setLevel(ERROR)
            logger.addHandler(slack_handler)
        except Exception:
            pass

    return logger


def create_music_charts_client(config, logger):
    return MusicChartsClient(
        base_url=config.get("base_url"),
        retry_limit=config.get("retry_limit", 3),
        retry_delay=config.get("retry_delay", 2),
        logger=logger
    )


def main():
    is_dev = setup_environment()
    env_vars = load_environment_variables()

    config = load_application_config(is_dev)
    if not config:
        sys.exit(1)

    config_values = extract_config_values(config)
    if not config_values:
        sys.exit(1)

    logger = setup_logging(is_dev, env_vars['slack_webhook_url'])

    music_charts_client = create_music_charts_client(
        config_values['raw_music_charts_config'],
        logger
    )

    logger.info("Pipeline started")

    try:
        raw_musiccharts_job(
            music_charts_client,
            config_values['raw_music_charts_config'],
            logger
        )
        logger.info("Pipeline completed successfully")
    except KeyboardInterrupt:
        logger.warning("Pipeline interrupted")
    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
