import time
from datetime import datetime, timedelta
from logging import Logger
from typing import List, Dict, Optional
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup


class MusicChartsClient:
    CHART_START_DATE = datetime(2020, 1, 4)
    MAX_CHART_ENTRIES = 100
    DEFAULT_TIMEOUT = 30

    def __init__(self, base_url: str, retry_limit: int = 3, retry_delay: int = 2, logger: Logger = None):
        self.base_url = base_url.rstrip('/')
        self.retry_limit = retry_limit
        self.retry_delay = retry_delay
        self.logger = logger

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def _log(self, level: str, message: str) -> None:
        if self.logger:
            getattr(self.logger, level)(message)
        else:
            print(f"[{level.upper()}] {message}")

    def _make_request(self, url: str) -> Optional[str]:
        for attempt in range(self.retry_limit):
            try:
                self._log("info", f"Requesting: {url} (attempt {attempt + 1})")

                response = self.session.get(url, timeout=self.DEFAULT_TIMEOUT)
                response.raise_for_status()

                return response.text

            except requests.exceptions.RequestException as e:
                self._log("warning", f"Request failed (attempt {attempt + 1}): {e}")

                if attempt < self.retry_limit - 1:
                    time.sleep(self.retry_delay)
                else:
                    self._log("error", f"Failed to fetch {url} after {self.retry_limit} attempts")

        return None

    def get_chart_dates(self, chart_type: str = "singles") -> List[str]:
        self._log("info", f"Generating {chart_type} chart dates")

        dates = []
        current_date = self.CHART_START_DATE
        end_date = datetime.now()

        while current_date <= end_date:
            dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=7)

        self._log("info", f"Generated {len(dates)} potential {chart_type} chart dates")
        return dates

    def _build_chart_url(self, chart_type: str, chart_date: str) -> str:
        if chart_type == "singles":
            return f"{self.base_url}/singles-chart/{chart_date}"
        elif chart_type == "albums":
            return f"{self.base_url}/album-chart/{chart_date}"
        else:
            raise ValueError(f"Unknown chart type: {chart_type}")

    def scrape_chart_data(self, chart_type: str, chart_date: str) -> List[Dict]:
        try:
            datetime.strptime(chart_date, "%Y-%m-%d")
        except ValueError:
            self._log("error", f"Invalid date format: {chart_date}")
            return []

        try:
            chart_url = self._build_chart_url(chart_type, chart_date)
        except ValueError as e:
            self._log("error", str(e))
            return []

        html_content = self._make_request(chart_url)
        if not html_content:
            return []

        return self._extract_chart_entries(html_content, chart_type, chart_date)

    def _extract_chart_entries(self, html_content: str, chart_type: str, chart_date: str) -> List[Dict]:
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            if chart_type == "singles":
                return self._extract_singles_entries(soup, chart_date)
            elif chart_type == "albums":
                return self._extract_albums_entries(soup, chart_date)
            else:
                self._log("error", f"Unknown chart type: {chart_type}")
                return []

        except Exception as e:
            self._log("error", f"Error parsing HTML for {chart_date}: {e}")
            return []

    def _parse_title_and_artist(self, text: str, href: str) -> tuple:
        title = ""
        artist = ""

        if ' - ' in text:
            parts = text.split(' - ', 1)
            title = parts[0].strip()
            artist = parts[1].strip()
        elif ' by ' in text:
            parts = text.split(' by ', 1)
            title = parts[0].strip()
            artist = parts[1].strip()
        else:
            title = text

            # Extract artist from URL
            if href:
                # Handle both relative and absolute URLs
                if href.startswith('http'):
                    # Full URL: https://musicchartsarchive.com/albums/taylor-swift/album-name
                    url_parts = href.split('/')
                    if len(url_parts) >= 5 and '/albums/' in href:
                        artist_slug = url_parts[4]  # Index 4 for full URLs
                        artist = artist_slug.replace('-', ' ').title()
                    elif len(url_parts) >= 5 and '/singles/' in href:
                        artist_slug = url_parts[4]  # Index 4 for full URLs
                        artist = artist_slug.replace('-', ' ').title()
                else:
                    # Relative URL: /albums/taylor-swift/album-name
                    url_parts = href.strip('/').split('/')
                    if len(url_parts) >= 2:
                        artist_slug = url_parts[1]  # Index 1 for relative URLs
                        artist = artist_slug.replace('-', ' ').title()

        return title, artist

    def _extract_singles_entries(self, soup: BeautifulSoup, chart_date: str) -> List[Dict]:
        entries = []
        position = 1

        try:
            singles_links = soup.find_all('a', href=True)

            for link in singles_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)

                if '/singles/' not in href:
                    continue

                if not text or len(text) < 3:
                    continue

                title, artist = self._parse_title_and_artist(text, href)

                title = self._clean_title_text(title)
                artist = self._clean_artist_text(artist)

                if not title or len(title) < 2:
                    continue

                full_url = urljoin(self.base_url, href) if not href.startswith('http') else href

                entry = {
                    "single_title": title,
                    "artist": artist,
                    "chart_date": chart_date,
                    "position": position,
                    "url": full_url
                }

                entries.append(entry)
                position += 1

                if position > self.MAX_CHART_ENTRIES:
                    break

            self._log("info", f"Extracted {len(entries)} singles entries for {chart_date}")
            return entries

        except Exception as e:
            self._log("error", f"Error extracting singles entries: {e}")
            return []

    def _extract_albums_entries(self, soup: BeautifulSoup, chart_date: str) -> List[Dict]:
        entries = []
        position = 1

        try:
            album_links = soup.find_all('a', href=lambda x: x and '/albums/' in x)

            for link in album_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)

                if not text or len(text) < 3:
                    continue

                title, artist = self._parse_title_and_artist(text, href)

                title = self._clean_title_text(title)
                artist = self._clean_artist_text(artist)

                if not title or len(title) < 2:
                    continue

                full_url = urljoin(self.base_url, href) if not href.startswith('http') else href

                entry = {
                    "album_title": title,
                    "artist": artist,
                    "chart_date": chart_date,
                    "position": position,
                    "url": full_url
                }

                entries.append(entry)
                position += 1

                if position > self.MAX_CHART_ENTRIES:
                    break

            self._log("info", f"Extracted {len(entries)} albums entries for {chart_date}")
            return entries

        except Exception as e:
            self._log("error", f"Error extracting albums entries: {e}")
            return []

    def _clean_title_text(self, text: str) -> str:
        if not text:
            return ""

        cleaned = text.strip().strip('"').strip("'").strip()

        if cleaned.endswith(' Song'):
            cleaned = cleaned[:-5]

        cleaned = cleaned.replace('\\"', '"')
        cleaned = cleaned.replace("\\'", "'")
        cleaned = cleaned.replace('\\', '')

        cleaned = ' '.join(cleaned.split())

        return cleaned

    def _clean_artist_text(self, text: str) -> str:
        if not text:
            return ""

        if ' | Music Charts Archive' in text:
            cleaned = text.replace(' | Music Charts Archive', '')
        else:
            cleaned = text

        return cleaned.strip()
