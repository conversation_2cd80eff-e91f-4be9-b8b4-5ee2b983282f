from datetime import datetime
from typing import List, Dict, Set, Optional


def get_title_field_for_chart_type(chart_type: str) -> str:
    return f"{chart_type[:-1]}_title"


def is_valid_position(position) -> bool:
    try:
        pos_int = int(position)
        return pos_int > 0
    except (ValueError, TypeError):
        return False


def is_valid_date(date_str: str) -> bool:
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def validate_single_chart_entry(entry: Dict, chart_type: str) -> Optional[Dict]:
    try:
        title_field = get_title_field_for_chart_type(chart_type)

        title = entry.get(title_field, "").strip()
        artist = entry.get("artist", "").strip()
        chart_date = entry.get("chart_date", "").strip()
        position = entry.get("position")

        # For albums, artist field might be empty, so we only require title, chart_date, and position
        if chart_type == "albums":
            if not title or not chart_date or position is None:
                return None
        else:
            # For singles, require all fields including artist
            if not title or not artist or not chart_date or position is None:
                return None

        if not is_valid_position(position):
            return None

        if not is_valid_date(chart_date):
            return None

        cleaned_entry = {
            title_field: title,
            "artist": artist,  # Keep artist field even if empty for albums
            "chart_date": chart_date,
            "position": int(position),
            "url": entry.get("url", "").strip()
        }

        return cleaned_entry

    except Exception:
        return None


def validate_chart_data(data: List[Dict], chart_type: str) -> List[Dict]:
    validated_data = []

    for entry in data:
        cleaned_entry = validate_single_chart_entry(entry, chart_type)
        if cleaned_entry:
            validated_data.append(cleaned_entry)

    return validated_data

def is_date_in_range(date_str: str, date_range: Dict) -> bool:
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")

        start_year = date_range.get("start_year")
        if start_year and date_obj.year < start_year:
            return False

        end_year = date_range.get("end_year")
        if end_year and date_obj.year > end_year:
            return False

        return True

    except ValueError:
        return False


def filter_new_dates(available_dates: List[str], existing_dates: Set[str],
                    date_range: Optional[Dict] = None) -> List[str]:
    new_dates = []

    for date_str in available_dates:
        if date_str in existing_dates:
            continue

        if date_range and not is_date_in_range(date_str, date_range):
            continue

        new_dates.append(date_str)

    new_dates.sort()
    return new_dates


def remove_duplicates_by_key(data: List[Dict], key_fields: List[str]) -> List[Dict]:
    seen_keys = set()
    unique_data = []

    for entry in data:
        key_values = tuple(entry.get(field, "") for field in key_fields)

        if key_values not in seen_keys:
            seen_keys.add(key_values)
            unique_data.append(entry)

    return unique_data
