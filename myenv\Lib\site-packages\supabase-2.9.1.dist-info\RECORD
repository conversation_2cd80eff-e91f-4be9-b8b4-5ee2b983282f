../../Scripts/tests.exe,sha256=HEULbd3eFTZx8kTTiuMCL9tZhfnVws0kpxp4CS2-wuY,108436
supabase-2.9.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
supabase-2.9.1.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
supabase-2.9.1.dist-info/METADATA,sha256=B0JNEY9yJQ3cpaMNTx_alRHA0AHz43loBfVMk9Ywkww,10861
supabase-2.9.1.dist-info/RECORD,,
supabase-2.9.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase-2.9.1.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
supabase-2.9.1.dist-info/entry_points.txt,sha256=F4onP9kSP0FoU2sCoOxrYbU-c60KwlZ_0quCskleaKg,50
supabase/__init__.py,sha256=GT3OMAL17yeubpRxEvU-cJrDsi_qwkxwvMuMBi-WaEs,2151
supabase/__pycache__/__init__.cpython-311.pyc,,
supabase/__pycache__/client.cpython-311.pyc,,
supabase/__pycache__/types.cpython-311.pyc,,
supabase/__pycache__/version.cpython-311.pyc,,
supabase/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_async/__pycache__/__init__.cpython-311.pyc,,
supabase/_async/__pycache__/auth_client.cpython-311.pyc,,
supabase/_async/__pycache__/client.cpython-311.pyc,,
supabase/_async/auth_client.py,sha256=32N1tIVdzTaxaOjPPtQkS-Qm4z7ICCIezvKKeMP7JqI,1249
supabase/_async/client.py,sha256=qF0DY-CU0iSXUN9UoVLiEUxZxOU7j6fbs564cfro7xI,11544
supabase/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_sync/__pycache__/__init__.cpython-311.pyc,,
supabase/_sync/__pycache__/auth_client.cpython-311.pyc,,
supabase/_sync/__pycache__/client.cpython-311.pyc,,
supabase/_sync/auth_client.py,sha256=0NMX9G9Qbrob1Fn1k2JAJLy0GqKF4LvV7LX5kyINSqs,1239
supabase/_sync/client.py,sha256=SU_SOZ-7u6NG--ErymN8ErQxaTRy4SXqMdYukdg9xwg,11385
supabase/client.py,sha256=NciJGgxVMZHIrGCrN-_iCDEjw_C2s2eYYaLUvkskOGE,2075
supabase/lib/__init__.py,sha256=hBGVFLg5RVk6liHGIUuak1crNBiz5m-mPvvdxv8nmNU,67
supabase/lib/__pycache__/__init__.cpython-311.pyc,,
supabase/lib/__pycache__/client_options.cpython-311.pyc,,
supabase/lib/client_options.py,sha256=e4zdfZLEHclLc0Vw_bABZYDoTY0E4G6uzDxPDiwuQBI,6798
supabase/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase/types.py,sha256=dvqNhqMQiGUjHJ8geOngvXlJ5wntGapM_pGnMBjwVrM,178
supabase/version.py,sha256=lGE1643qT0wb4hC8z8UM-c3b7UDPiSdV0qCa0MTgUBI,52
