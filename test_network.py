import os
import socket
import requests
from urllib.parse import urlparse

def setup_environment():
    """Load environment variables from .env file"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✓ Environment variables loaded")
    except ImportError:
        print("⚠ python-dotenv not installed")
    except Exception as e:
        print(f"⚠ Error loading .env: {e}")

def test_dns_resolution():
    """Test DNS resolution for Supabase URL"""
    supabase_url = os.getenv("SUPABASE_URL")
    if not supabase_url:
        print("❌ SUPABASE_URL not found in environment")
        return False
    
    try:
        parsed_url = urlparse(supabase_url)
        hostname = parsed_url.hostname
        print(f"Testing DNS resolution for: {hostname}")
        
        # Test DNS resolution
        ip_address = socket.gethostbyname(hostname)
        print(f"✓ DNS resolution successful: {hostname} -> {ip_address}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing DNS: {e}")
        return False

def test_http_connectivity():
    """Test HTTP connectivity to Supabase"""
    supabase_url = os.getenv("SUPABASE_URL")
    if not supabase_url:
        print("❌ SUPABASE_URL not found")
        return False
    
    try:
        print(f"Testing HTTP connectivity to: {supabase_url}")
        
        # Test basic HTTP connectivity
        response = requests.get(f"{supabase_url}/rest/v1/", timeout=10)
        print(f"✓ HTTP connectivity successful: Status {response.status_code}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ HTTP connectivity failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing HTTP: {e}")
        return False

def test_supabase_api():
    """Test Supabase API connectivity"""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials")
        return False
    
    try:
        print("Testing Supabase API connectivity...")
        
        headers = {
            "apikey": supabase_key,
            "Authorization": f"Bearer {supabase_key}",
            "Content-Type": "application/json"
        }
        
        # Test API endpoint
        response = requests.get(
            f"{supabase_url}/rest/v1/music_archive?select=id&limit=1",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✓ Supabase API connectivity successful")
            return True
        else:
            print(f"⚠ Supabase API returned status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Supabase API connectivity failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Supabase API: {e}")
        return False

def main():
    """Run network connectivity tests"""
    print("=" * 60)
    print("🌐 NETWORK CONNECTIVITY TEST FOR SUPABASE")
    print("=" * 60)
    
    setup_environment()
    
    tests = [
        ("DNS Resolution", test_dns_resolution),
        ("HTTP Connectivity", test_http_connectivity),
        ("Supabase API", test_supabase_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}...")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL NETWORK TESTS PASSED!")
        print("Your Supabase connection should work properly.")
    else:
        print("⚠ SOME NETWORK TESTS FAILED!")
        print("This explains why your database saves are failing.")
        print("\nPossible solutions:")
        print("1. Check your internet connection")
        print("2. Try using a VPN if behind a firewall")
        print("3. Check if your network blocks Supabase domains")
        print("4. Verify your Supabase URL and key are correct")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
